const globals = {
  "AutoMount": true,
  "AutoMountOptions": true,
  "Browser": true,
  "CacheEntry": true,
  "CacheMetadata": true,
  "CacheStats": true,
  "ChunkingOptions": true,
  "Component": true,
  "ComponentPublicInstance": true,
  "ComputedRef": true,
  "ContentIndexer": true,
  "ContentScriptAnchoredOptions": true,
  "ContentScriptAppendMode": true,
  "ContentScriptContext": true,
  "ContentScriptInlinePositioningOptions": true,
  "ContentScriptModalPositioningOptions": true,
  "ContentScriptOverlayAlignment": true,
  "ContentScriptOverlayPositioningOptions": true,
  "ContentScriptPositioningOptions": true,
  "ContentScriptUi": true,
  "ContentScriptUiOptions": true,
  "DirectiveBinding": true,
  "EffectScope": true,
  "ExtractDefaultPropTypes": true,
  "ExtractPropTypes": true,
  "ExtractPublicPropTypes": true,
  "IframeContentScriptUi": true,
  "IframeContentScriptUiOptions": true,
  "IndexingOptions": true,
  "InjectScriptOptions": true,
  "InjectionKey": true,
  "IntegratedContentScriptUi": true,
  "IntegratedContentScriptUiOptions": true,
  "InvalidMatchPattern": true,
  "MatchPattern": true,
  "MaybeRef": true,
  "MaybeRefOrGetter": true,
  "MigrationError": true,
  "ModelCacheManager": true,
  "ModelPreset": true,
  "OffscreenManager": true,
  "PREDEFINED_MODELS": true,
  "PropType": true,
  "Ref": true,
  "SIMDMathEngine": true,
  "ScriptPublicPath": true,
  "SearchResult": true,
  "SemanticSimilarityEngine": true,
  "SemanticSimilarityEngineProxy": true,
  "ShadowRootContentScriptUi": true,
  "ShadowRootContentScriptUiOptions": true,
  "ShallowRef": true,
  "Slot": true,
  "Slots": true,
  "StopAutoMount": true,
  "StorageArea": true,
  "StorageAreaChanges": true,
  "StorageItemKey": true,
  "TextChunk": true,
  "TextChunker": true,
  "VNode": true,
  "VectorDatabase": true,
  "VectorDatabaseConfig": true,
  "VectorDocument": true,
  "WritableComputedRef": true,
  "WxtAppConfig": true,
  "WxtStorage": true,
  "WxtStorageItem": true,
  "WxtWindowEventMap": true,
  "browser": true,
  "canvasToDataURL": true,
  "cleanupModelCache": true,
  "clearAllVectorData": true,
  "clearModelCache": true,
  "compareModels": true,
  "compressImage": true,
  "computed": true,
  "createApp": true,
  "createIframeUi": true,
  "createImageBitmapFromUrl": true,
  "createIntegratedUi": true,
  "createShadowRootUi": true,
  "cropAndResizeImage": true,
  "customRef": true,
  "defineAppConfig": true,
  "defineAsyncComponent": true,
  "defineBackground": true,
  "defineComponent": true,
  "defineContentScript": true,
  "defineUnlistedScript": true,
  "defineWxtPlugin": true,
  "effectScope": true,
  "fakeBrowser": true,
  "getAllModelSizes": true,
  "getCacheStats": true,
  "getCurrentInstance": true,
  "getCurrentScope": true,
  "getCurrentWatcher": true,
  "getGlobalContentIndexer": true,
  "getGlobalVectorDatabase": true,
  "getGlobalVectorDatabaseSync": true,
  "getMessage": true,
  "getModelIdentifierWithVersion": true,
  "getModelInfo": true,
  "getModelSizeInfo": true,
  "getOnnxFileNameForVersion": true,
  "h": true,
  "hasAnyModelCache": true,
  "inject": true,
  "injectScript": true,
  "isDefaultModelCached": true,
  "isI18nAvailable": true,
  "isProxy": true,
  "isReactive": true,
  "isReadonly": true,
  "isRef": true,
  "isShallow": true,
  "listAvailableModels": true,
  "lruCache": true,
  "markRaw": true,
  "nextTick": true,
  "offscreenManager": true,
  "onActivated": true,
  "onBeforeMount": true,
  "onBeforeUnmount": true,
  "onBeforeUpdate": true,
  "onDeactivated": true,
  "onErrorCaptured": true,
  "onMounted": true,
  "onRenderTracked": true,
  "onRenderTriggered": true,
  "onScopeDispose": true,
  "onServerPrefetch": true,
  "onUnmounted": true,
  "onUpdated": true,
  "onWatcherCleanup": true,
  "provide": true,
  "reactive": true,
  "readonly": true,
  "recommendModelForDevice": true,
  "recommendModelForLanguage": true,
  "ref": true,
  "resetGlobalVectorDatabase": true,
  "resolveComponent": true,
  "shallowReactive": true,
  "shallowReadonly": true,
  "shallowRef": true,
  "stitchImages": true,
  "storage": true,
  "toRaw": true,
  "toRef": true,
  "toRefs": true,
  "toValue": true,
  "triggerRef": true,
  "unref": true,
  "useAppConfig": true,
  "useAttrs": true,
  "useCssModule": true,
  "useCssVars": true,
  "useId": true,
  "useModel": true,
  "useSlots": true,
  "useTemplateRef": true,
  "watch": true,
  "watchEffect": true,
  "watchPostEffect": true,
  "watchSyncEffect": true
}

export default {
  name: "wxt/auto-imports",
  languageOptions: {
    globals,
    /** @type {import('eslint').Linter.SourceType} */
    sourceType: "module",
  },
};
