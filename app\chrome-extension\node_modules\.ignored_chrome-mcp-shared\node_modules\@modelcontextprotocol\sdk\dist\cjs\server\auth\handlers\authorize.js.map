{"version": 3, "file": "authorize.js", "sourceRoot": "", "sources": ["../../../../../src/server/auth/handlers/authorize.ts"], "names": [], "mappings": ";;;;;AAuCA,oDAmHC;AAzJD,6BAAwB;AACxB,sDAA8B;AAE9B,2DAA4E;AAC5E,uEAAiE;AACjE,4CAOsB;AAWtB,iEAAiE;AACjE,MAAM,+BAA+B,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/C,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;IACrB,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;CACnJ,CAAC,CAAC;AAEH,yHAAyH;AACzH,MAAM,gCAAgC,GAAG,OAAC,CAAC,MAAM,CAAC;IAChD,aAAa,EAAE,OAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IAChC,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE;IAC1B,qBAAqB,EAAE,OAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IACxC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC7B,CAAC,CAAC;AAEH,SAAgB,oBAAoB,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,eAAe,EAA+B;IACxG,sCAAsC;IACtC,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;IAChC,MAAM,CAAC,GAAG,CAAC,IAAA,kCAAc,EAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5C,MAAM,CAAC,GAAG,CAAC,iBAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAEpD,iDAAiD;IACjD,IAAI,eAAe,KAAK,KAAK,EAAE,CAAC;QAC9B,MAAM,CAAC,GAAG,CAAC,IAAA,8BAAS,EAAC;YACnB,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;YACvC,GAAG,EAAE,GAAG,EAAE,4BAA4B;YACtC,eAAe,EAAE,IAAI;YACrB,aAAa,EAAE,KAAK;YACpB,OAAO,EAAE,IAAI,gCAAoB,CAAC,6DAA6D,CAAC,CAAC,gBAAgB,EAAE;YACnH,GAAG,eAAe;SACnB,CAAC,CAAC,CAAC;IACN,CAAC;IAED,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;;QACjC,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;QAE3C,mEAAmE;QACnE,oDAAoD;QACpD,2DAA2D;QAE3D,0FAA0F;QAC1F,IAAI,SAAS,EAAE,YAAY,EAAE,MAAM,CAAC;QACpC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,+BAA+B,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACvG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,IAAI,+BAAmB,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACtD,CAAC;YAED,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAClC,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;YAExC,MAAM,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAC1D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,8BAAkB,CAAC,mBAAmB,CAAC,CAAC;YACpD,CAAC;YAED,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;gBAC/B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBACjD,MAAM,IAAI,+BAAmB,CAAC,2BAA2B,CAAC,CAAC;gBAC7D,CAAC;YACH,CAAC;iBAAM,IAAI,MAAM,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7C,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,+BAAmB,CAAC,yEAAyE,CAAC,CAAC;YAC3G,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,+CAA+C;YAC/C,EAAE;YACF,yEAAyE;YACzE,uEAAuE;YACvE,uEAAuE;YACvE,eAAe;YACf,IAAI,KAAK,YAAY,sBAAU,EAAE,CAAC;gBAChC,MAAM,MAAM,GAAG,KAAK,YAAY,uBAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBACxD,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACpD,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;gBAC5D,MAAM,WAAW,GAAG,IAAI,uBAAW,CAAC,uBAAuB,CAAC,CAAC;gBAC7D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACvD,CAAC;YAED,OAAO;QACT,CAAC;QAED,yFAAyF;QACzF,IAAI,KAAK,CAAC;QACV,IAAI,CAAC;YACH,8CAA8C;YAC9C,MAAM,WAAW,GAAG,gCAAgC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC7G,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBACzB,MAAM,IAAI,+BAAmB,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC3D,CAAC;YAED,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC;YACnD,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;YAE/B,kBAAkB;YAClB,IAAI,eAAe,GAAa,EAAE,CAAC;YACnC,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACxB,eAAe,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACnC,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,MAAA,MAAM,CAAC,KAAK,0CAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;gBAExD,oDAAoD;gBACpD,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;oBACpC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC9B,MAAM,IAAI,6BAAiB,CAAC,wCAAwC,KAAK,EAAE,CAAC,CAAC;oBAC/E,CAAC;gBACH,CAAC;YACH,CAAC;YAED,oDAAoD;YACpD,MAAM,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE;gBAC/B,KAAK;gBACL,MAAM,EAAE,eAAe;gBACvB,WAAW,EAAE,YAAY;gBACzB,aAAa,EAAE,cAAc;aAC9B,EAAE,GAAG,CAAC,CAAC;QACV,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,wDAAwD;YACxD,IAAI,KAAK,YAAY,sBAAU,EAAE,CAAC;gBAChC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,mBAAmB,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YACrE,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;gBAC/D,MAAM,WAAW,GAAG,IAAI,uBAAW,CAAC,uBAAuB,CAAC,CAAC;gBAC7D,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,mBAAmB,CAAC,YAAY,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,WAAmB,EAAE,KAAiB,EAAE,KAAc;IACjF,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC;IACtC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;IACpD,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAC9D,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;QACnB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;IACzD,CAAC;IACD,IAAI,KAAK,EAAE,CAAC;QACV,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC5C,CAAC;IACD,OAAO,QAAQ,CAAC,IAAI,CAAC;AACvB,CAAC"}