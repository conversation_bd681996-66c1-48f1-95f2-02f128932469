/* eslint-disable */
// element-highlighter-helper.js
// This script is injected into the page to highlight HTML elements with colored borders

(function () {
  // Prevent re-initialization
  if (window.__ELEMENT_HIGHLIGHTER_HELPER_INITIALIZED__) {
    return;
  }
  window.__ELEMENT_HIGHLIGHTER_HELPER_INITIALIZED__ = true;

  /**
   * Element highlighting configuration with colors and priorities
   * 元素高亮配置，包含颜色和优先级
   */
  const ELEMENT_HIGHLIGHT_CONFIG = {
    // Interactive elements - High priority, prominent colors
    // 交互元素 - 高优先级，醒目颜色
    button: { color: '#FF6B6B', priority: 10, borderWidth: '3px' },
    input: { color: '#4ECDC4', priority: 9, borderWidth: '2px' },
    a: { color: '#45B7D1', priority: 8, borderWidth: '2px' },
    select: { color: '#96CEB4', priority: 8, borderWidth: '2px' },
    textarea: { color: '#F39C12', priority: 8, borderWidth: '2px' },
    
    // Heading elements - Medium-high priority
    // 标题元素 - 中高优先级
    h1: { color: '#FECA57', priority: 7, borderWidth: '2px' },
    h2: { color: '#FF9FF3', priority: 6, borderWidth: '2px' },
    h3: { color: '#54A0FF', priority: 6, borderWidth: '2px' },
    h4: { color: '#5F27CD', priority: 5, borderWidth: '1px' },
    h5: { color: '#00D2D3', priority: 5, borderWidth: '1px' },
    h6: { color: '#FF9F43', priority: 5, borderWidth: '1px' },
    
    // Text elements - Medium priority  
    // 文本元素 - 中等优先级
    p: { color: '#A55EEA', priority: 4, borderWidth: '1px' },
    span: { color: '#26D0CE', priority: 3, borderWidth: '1px' },
    strong: { color: '#FD79A8', priority: 4, borderWidth: '1px' },
    em: { color: '#6C5CE7', priority: 4, borderWidth: '1px' },
    
    // Container elements - Low priority, thin borders
    // 容器元素 - 低优先级，细边框
    div: { color: '#DDA0DD', priority: 2, borderWidth: '1px' },
    section: { color: '#F7DC6F', priority: 3, borderWidth: '1px' },
    article: { color: '#AED6F1', priority: 3, borderWidth: '1px' },
    aside: { color: '#D5A6BD', priority: 3, borderWidth: '1px' },
    header: { color: '#F8C471', priority: 3, borderWidth: '1px' },
    footer: { color: '#82E0AA', priority: 3, borderWidth: '1px' },
    nav: { color: '#BB8FCE', priority: 4, borderWidth: '1px' },
    main: { color: '#85C1E9', priority: 3, borderWidth: '1px' },
    
    // List elements
    // 列表元素
    ul: { color: '#F8D7DA', priority: 3, borderWidth: '1px' },
    ol: { color: '#D1ECF1', priority: 3, borderWidth: '1px' },
    li: { color: '#D4EDDA', priority: 3, borderWidth: '1px' },
    
    // Table elements
    // 表格元素
    table: { color: '#FCF3CF', priority: 4, borderWidth: '1px' },
    tr: { color: '#FADBD8', priority: 3, borderWidth: '1px' },
    td: { color: '#E8F8F5', priority: 3, borderWidth: '1px' },
    th: { color: '#EBF5FB', priority: 4, borderWidth: '1px' },
    
    // Form elements
    // 表单元素
    form: { color: '#E8DAEF', priority: 4, borderWidth: '1px' },
    fieldset: { color: '#D5DBDB', priority: 3, borderWidth: '1px' },
    legend: { color: '#F4F6F6', priority: 4, borderWidth: '1px' },
    label: { color: '#FDEBD0', priority: 4, borderWidth: '1px' },
    
    // Image and media elements
    // 图片和媒体元素
    img: { color: '#FADBD8', priority: 5, borderWidth: '2px' },
    video: { color: '#D5F4E6', priority: 5, borderWidth: '2px' },
    audio: { color: '#FFEAA7', priority: 5, borderWidth: '2px' },
    canvas: { color: '#A29BFE', priority: 5, borderWidth: '2px' },
  };

  /**
   * Color schemes for different highlighting modes
   * 不同高亮模式的颜色方案
   */
  const COLOR_SCHEMES = {
    default: ELEMENT_HIGHLIGHT_CONFIG,
    rainbow: generateRainbowScheme(),
    monochrome: generateMonochromeScheme(),
  };

  /**
   * Generate rainbow color scheme
   */
  function generateRainbowScheme() {
    const rainbowColors = [
      '#FF0000', '#FF8000', '#FFFF00', '#80FF00', '#00FF00', 
      '#00FF80', '#00FFFF', '#0080FF', '#0000FF', '#8000FF', 
      '#FF00FF', '#FF0080'
    ];
    
    const scheme = {};
    const elementTypes = Object.keys(ELEMENT_HIGHLIGHT_CONFIG);
    
    elementTypes.forEach((type, index) => {
      const originalConfig = ELEMENT_HIGHLIGHT_CONFIG[type];
      scheme[type] = {
        ...originalConfig,
        color: rainbowColors[index % rainbowColors.length],
      };
    });
    
    return scheme;
  }

  /**
   * Generate monochrome color scheme
   */
  function generateMonochromeScheme() {
    const scheme = {};
    Object.keys(ELEMENT_HIGHLIGHT_CONFIG).forEach((type) => {
      const originalConfig = ELEMENT_HIGHLIGHT_CONFIG[type];
      // Use different shades of blue based on priority
      const blueShades = {
        10: '#000080', 9: '#1E3A8A', 8: '#1D4ED8', 7: '#2563EB',
        6: '#3B82F6', 5: '#60A5FA', 4: '#93C5FD', 3: '#BFDBFE', 2: '#DBEAFE', 1: '#EFF6FF'
      };
      scheme[type] = {
        ...originalConfig,
        color: blueShades[originalConfig.priority] || '#93C5FD',
      };
    });
    return scheme;
  }

  let currentHighlightId = null;
  let highlightedElements = [];

  /**
   * Check if an element is visible
   */
  function isElementVisible(element) {
    if (!element || !element.isConnected) return false;

    const style = window.getComputedStyle(element);
    if (
      style.display === 'none' ||
      style.visibility === 'hidden' ||
      style.opacity === '0'
    ) {
      return false;
    }

    const rect = element.getBoundingClientRect();
    return rect.width > 0 || rect.height > 0;
  }

  /**
   * Create unique CSS selector for highlighting
   */
  function createHighlightSelector(elementType, config) {
    return `
      [data-chrome-mcp-highlight="${elementType}"] {
        border: ${config.borderWidth} solid ${config.color} !important;
        box-sizing: border-box !important;
      }
    `;
  }

  /**
   * Highlight page elements
   */
  function highlightPageElements(elementTypes = [], colorScheme = 'default') {
    try {
      // Remove existing highlights first
      removePageHighlights();

      // Get color scheme configuration
      const schemeConfig = COLOR_SCHEMES[colorScheme] || COLOR_SCHEMES.default;
      
      // Determine which element types to highlight
      const typesToHighlight = elementTypes.length > 0 
        ? elementTypes.filter(type => schemeConfig[type])
        : Object.keys(schemeConfig);

      if (typesToHighlight.length === 0) {
        return { success: false, error: 'No valid element types specified' };
      }

      // Create unique style sheet ID
      currentHighlightId = `__chrome_mcp_element_highlighter_${Date.now()}__`;
      
      // Create and inject CSS styles
      const styleElement = document.createElement('style');
      styleElement.id = currentHighlightId;
      
      let cssRules = '';
      typesToHighlight.forEach(elementType => {
        const config = schemeConfig[elementType];
        if (config) {
          cssRules += createHighlightSelector(elementType, config);
        }
      });
      
      styleElement.textContent = cssRules;
      document.head.appendChild(styleElement);

      // Find and mark elements for highlighting
      let elementsCount = 0;
      typesToHighlight.forEach(elementType => {
        const elements = document.querySelectorAll(elementType);
        elements.forEach(element => {
          if (isElementVisible(element)) {
            element.setAttribute('data-chrome-mcp-highlight', elementType);
            highlightedElements.push(element);
            elementsCount++;
          }
        });
      });

      console.log(`Highlighted ${elementsCount} elements with color scheme: ${colorScheme}`);
      
      return {
        success: true,
        elementsHighlighted: elementsCount,
        colorScheme: colorScheme,
        elementTypes: typesToHighlight,
      };
    } catch (error) {
      console.error('Error highlighting elements:', error);
      return { 
        success: false, 
        error: `Failed to highlight elements: ${error.message}` 
      };
    }
  }

  /**
   * Remove page highlights
   */
  function removePageHighlights() {
    try {
      // Remove CSS styles
      if (currentHighlightId) {
        const styleElement = document.getElementById(currentHighlightId);
        if (styleElement) {
          styleElement.remove();
        }
        currentHighlightId = null;
      }

      // Remove data attributes from elements
      highlightedElements.forEach(element => {
        if (element && element.isConnected) {
          element.removeAttribute('data-chrome-mcp-highlight');
        }
      });
      highlightedElements = [];

      return { success: true, message: 'All highlights removed' };
    } catch (error) {
      console.error('Error removing highlights:', error);
      return { 
        success: false, 
        error: `Failed to remove highlights: ${error.message}` 
      };
    }
  }

  // Listen for messages from the extension
  chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    if (request.action === 'highlightPageElements') {
      const result = highlightPageElements(request.elementTypes, request.colorScheme);
      sendResponse(result);
      return true; // Indicates async response
    } else if (request.action === 'removePageHighlights') {
      const result = removePageHighlights();
      sendResponse(result);
      return true; // Indicates async response
    } else if (request.action === 'chrome_highlight_page_elements_ping') {
      sendResponse({ status: 'pong' });
      return false;
    }
  });

  console.log('Element highlighter helper script loaded');
})();