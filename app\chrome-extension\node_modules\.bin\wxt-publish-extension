#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/g/releases/mcp-chrome-20250808-stable/node_modules/.pnpm/wxt@0.20.7_@types+node@22.1_ddd9a78639e45b282dbc16bc0f57bf05/node_modules/wxt/bin/node_modules:/mnt/g/releases/mcp-chrome-20250808-stable/node_modules/.pnpm/wxt@0.20.7_@types+node@22.1_ddd9a78639e45b282dbc16bc0f57bf05/node_modules/wxt/node_modules:/mnt/g/releases/mcp-chrome-20250808-stable/node_modules/.pnpm/wxt@0.20.7_@types+node@22.1_ddd9a78639e45b282dbc16bc0f57bf05/node_modules:/mnt/g/releases/mcp-chrome-20250808-stable/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/g/releases/mcp-chrome-20250808-stable/node_modules/.pnpm/wxt@0.20.7_@types+node@22.1_ddd9a78639e45b282dbc16bc0f57bf05/node_modules/wxt/bin/node_modules:/mnt/g/releases/mcp-chrome-20250808-stable/node_modules/.pnpm/wxt@0.20.7_@types+node@22.1_ddd9a78639e45b282dbc16bc0f57bf05/node_modules/wxt/node_modules:/mnt/g/releases/mcp-chrome-20250808-stable/node_modules/.pnpm/wxt@0.20.7_@types+node@22.1_ddd9a78639e45b282dbc16bc0f57bf05/node_modules:/mnt/g/releases/mcp-chrome-20250808-stable/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../wxt/bin/wxt-publish-extension.cjs" "$@"
else
  exec node  "$basedir/../wxt/bin/wxt-publish-extension.cjs" "$@"
fi
