{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noEmit": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "strict": true, "skipLibCheck": true, "paths": {"@": [".."], "@/*": ["../*"], "~": [".."], "~/*": ["../*"], "@@": [".."], "@@/*": ["../*"], "~~": [".."], "~~/*": ["../*"]}}, "include": ["../**/*", "./wxt.d.ts"], "exclude": ["../.output"]}