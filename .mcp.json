{"mcpServers": {"mcp-feedback-collector": {"type": "stdio", "command": "/mnt/d/Programs/Python/Python311/python.exe", "args": ["-m", "mcp_feedback_collector.server"], "env": {"PYTHONIOENCODING": "utf-8", "MCP_DIALOG_TIMEOUT": "1200", "PYTHONPATH": "/mnt/d/Programs/Python/Python311/Lib/site-packages"}}, "context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {}}, "chrome-mcp-stdio": {"type": "stdio", "command": "node", "args": ["app/native-server/dist/mcp/mcp-server-stdio.js"], "env": {"MCP_SERVER_URL": "http://127.0.0.1:12306/mcp", "MCP_HOST": "127.0.0.1", "MCP_PORT": "12306"}}}}